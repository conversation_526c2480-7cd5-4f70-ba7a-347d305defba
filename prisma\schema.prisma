generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model User {
  id                     String                     @id @default(uuid())
  phoneNumber            String                     @unique
  firstName              String
  lastName               String
  password               String
  isPhoneVerified        Boolean                    @default(false)
  verificationCode       String?
  driverStatus           DriverStatus               @default(NONE)
  IdCardBackUrl          String?
  IdCardFrontUrl         String?
  PersonalPhotoUrl       String?
  verificationCodeSentAt DateTime?
  createdAt              DateTime                   @default(now())
  isSuperAdmin           <PERSON>                    @default(false)
  car                    Car?
  markedPlaces           MarkedPlace[]
  suggestedOrders        Order[]                    @relation("SuggestedOrders")
  orders                 Order[]
  refreshTokens          RefreshToken[]
  transactionsFrom       MoneyTransaction[]         @relation("TransactionsFrom")
  transactionsTo         MoneyTransaction[]         @relation("TransactionsTo")
  trips                  Trip[]                     @relation("DriverTrips")
  collaborativeTrips     CollaborativeTripOffer[]   @relation("DriverCollaborativeTrips")
  bookings               CollaborativeTripBooking[] @relation("PassengerBookings")

  @@index([phoneNumber])
  @@index([driverStatus])
}

model Car {
  id           String     @id @default(uuid())
  userId       String     @unique
  make         String
  model        String
  year         Int
  licensePlate String?
  user         User       @relation(fields: [userId], references: [id])
  photos       CarPhoto[] @relation("CarPhotos")

  @@index([userId])
  @@index([make, model])
}

model CarPhoto {
  id       String @id @default(uuid())
  carId    String
  photoUrl String
  car      Car    @relation("CarPhotos", fields: [carId], references: [id])

  @@index([carId])
}

model StopPoint {
  id        String @id @default(uuid())
  name      String
  latitude  Float
  longitude Float

  // Relations for bookings
  pickupBookings  CollaborativeTripBooking[] @relation("BookingPickupPoints")
  dropoffBookings CollaborativeTripBooking[] @relation("BookingDropoffPoints")
}

model MarkedPlace {
  id        String @id @default(uuid())
  userId    String
  name      String
  latitude  Float
  longitude Float
  user      User   @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Point {
  id                       String                  @id @default(uuid())
  latitude                 Float
  longitude                Float
  dropoffOrders            Order[]                 @relation("DropoffPoint")
  pickupOrders             Order[]                 @relation("PickupPoint")
  collaborativeTripOfferId String?
  CollaborativeTripOffer   CollaborativeTripOffer? @relation(fields: [collaborativeTripOfferId], references: [id])
  CollaborativeTripOffer   CollaborativeTripOffer? @relation(fields: [collaborativeTripOfferId], references: [id])
}

model Order {
  id                    String             @id @default(uuid())
  userId                String
  status                OrderStatus        @default(PENDING)
  createdAt             DateTime           @default(now())
  pickupPointId         String?
  dropoffPointId        String?
  tripId                String?            @unique
  lastSuggestedAt       DateTime?
  lastSuggestedDriverId String?
  finalPrice            Float?
  initialPrice          Float?
  dropoffPoint          Point?             @relation("DropoffPoint", fields: [dropoffPointId], references: [id])
  lastSuggestedDriver   User?              @relation("SuggestedOrders", fields: [lastSuggestedDriverId], references: [id])
  pickupPoint           Point?             @relation("PickupPoint", fields: [pickupPointId], references: [id])
  trip                  Trip?              @relation(fields: [tripId], references: [id])
  user                  User               @relation(fields: [userId], references: [id])
  transactions          MoneyTransaction[]

  @@index([userId])
  @@index([pickupPointId])
  @@index([dropoffPointId])
  @@index([lastSuggestedDriverId])
  @@index([tripId])
}

model Trip {
  id                       String     @id @default(uuid())
  driverId                 String
  status                   TripStatus @default(DRIVER_DIDNT_ARRIVE)
  currentLocationLatitude  Float?
  currentLocationLongitude Float?
  createdAt                DateTime   @default(now())

  // Trip timing fields (in minutes)
  timeTillDriverArrived      Int? // Time from order creation to driver arrival
  timeDriverWaitingForClient Int? // Time driver waited for client
  actualTripTime             Int? // Time client was with driver (actual trip duration)

  // Car route tracking
  carRoute Json? // Array of points: [{lat: number, lng: number, timestamp: string}]

  order  Order?
  driver User   @relation("DriverTrips", fields: [driverId], references: [id])

  @@index([driverId])
}

enum DriverStatus {
  NONE
  IN_REVIEW
  PENDING_ONSITE_REVIEW
  REJECTED
  APPROVED
}

enum OrderStatus {
  PENDING
  SUGGESTED_FOR_DRIVER
  CONFIRMED
  COMPLETED
  CANCLED
}

enum TripStatus {
  DRIVER_DIDNT_ARRIVE
  DRIVER_WAITING_CLIENT
  DRIVER_WITH_CLIENT
  FINISHED
  CANCLED
}

enum TransactionType {
  CLIENT_TO_DRIVER
  DRIVER_TO_COMPANY
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}

model MoneyTransaction {
  id     String            @id @default(uuid())
  type   TransactionType
  status TransactionStatus @default(PENDING)
  amount Float // Amount in USD

  // Order reference
  orderId String

  // Collaborative trip reference
  collaborativeTripId String?

  // Participants
  fromUserId String? // Null for company transactions
  toUserId   String? // Null for company transactions

  // Metadata
  description String?
  createdAt   DateTime  @default(now())
  completedAt DateTime?

  // Relations
  order             Order                   @relation(fields: [orderId], references: [id])
  fromUser          User?                   @relation("TransactionsFrom", fields: [fromUserId], references: [id])
  toUser            User?                   @relation("TransactionsTo", fields: [toUserId], references: [id])
  collaborativeTrip CollaborativeTripOffer? @relation("CollaborativeTripTransactions", fields: [collaborativeTripId], references: [id])

  @@index([orderId])
  @@index([fromUserId])
  @@index([toUserId])
  @@index([collaborativeTripId])
  @@index([type])
  @@index([status])
  @@map("Transaction")
}

model CollaborativeTripOffer {
  id        String   @id @default(uuid())
  driverId  String
  createdAt DateTime @default(now())

  scheduledTime DateTime
  startpoint    String
  endpoint      String

  routePoints Json // Array of {lat: number, lng: number} from Valhalla
  totalTime   Float // in minutes

  availableSeats Int
  pricePerSeat   Float // in USD

  // Client bookings tracking
  clientBookings        Json // Array of {clientId: string, seatsBooked: number, pickupPointId: string, dropoffPointId: string, status: 'BOOKED' | 'REJECTED'}
  possiblePickupPoints  Json // Array of stop point IDs that can be used for pickup
  possibleDropoffPoints Json // Array of stop point IDs that can be used for dropoff

  // Trip metadata
  driverNotes String?

  // Relations
  driver       User                       @relation("DriverCollaborativeTrips", fields: [driverId], references: [id])
  startPoint   Point                      @relation("CollaborativeTripStartPoint", fields: [startpoint], references: [id])
  endPoint     Point                      @relation("CollaborativeTripEndPoint", fields: [endpoint], references: [id])
  waypoints    Point[]                    @relation("CollaborativeTripWaypoints")
  transactions MoneyTransaction[]         @relation("CollaborativeTripTransactions")
  bookings     CollaborativeTripBooking[] @relation("CollaborativeTripBookings")
  Point        Point[]

  @@index([driverId])
  @@index([scheduledTime])
  @@index([startpoint])
  @@index([endpoint])
}

enum BookingStatus {
  BOOKED // Booking was successful and confirmed
  PENDING // Booking is pending confirmation
  REJECTED // Booking was rejected by driver
}

model CollaborativeTripBooking {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Trip reference
  collaborativeTripOfferId String

  // Passenger details
  passengerId        String
  numberOfPassengers Int

  // Pickup and dropoff points
  pickupPointId  String
  dropoffPointId String

  // Booking status
  status BookingStatus @default(PENDING)

  // Total cost for this booking
  totalCost Float

  // Optional notes from passenger
  passengerNotes String?

  // Optional notes from driver (e.g., cancellation reason)
  cancelationReason String?

  // Relations
  collaborativeTripOffer CollaborativeTripOffer @relation("CollaborativeTripBookings", fields: [collaborativeTripOfferId], references: [id])
  passenger              User                   @relation("PassengerBookings", fields: [passengerId], references: [id])
  pickupPoint            StopPoint              @relation("BookingPickupPoints", fields: [pickupPointId], references: [id])
  dropoffPoint           StopPoint              @relation("BookingDropoffPoints", fields: [dropoffPointId], references: [id])

  @@index([collaborativeTripOfferId])
  @@index([passengerId])
  @@index([status])
  @@index([pickupPointId])
  @@index([dropoffPointId])
}
