import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
    FindMatchingTripsDto,
    BookOfferDto,
    CollaborativeTripBooking,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export type {
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
    FindMatchingTripsDto,
    BookOfferDto,
    CollaborativeTripBooking,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class CollaborativeTripService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Create a new collaborative trip offer (driver only)
     * @param createDto Collaborative trip offer creation data
     * @returns Observable with created CollaborativeTripOffer data
     */
    createCollaborativeTripOffer(
        createDto: CreateCollaborativeTripOfferDto,
    ): Observable<
        | { data: CollaborativeTripOffer; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips',
            opj: createDto,
            des: this.destroyRef,
            successMessage: 'Collaborative trip offer created successfully',
            failedMessage: 'Failed to create collaborative trip offer',
        };
        return this.http.post<CollaborativeTripOffer>(options);
    }

    /**
     * Get recent collaborative trip offers for the current driver
     * Recent offers are those scheduled within the next 24 hours
     * @returns Observable with array of recent CollaborativeTripOffer data
     */
    getMyRecentCollaborativeTripOffers(): Observable<
        | { data: CollaborativeTripOffer[]; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/my/recent',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }

    /**
     * Get old collaborative trip offers for the current driver
     * Old offers are those scheduled more than 24 hours ago
     * @returns Observable with array of old CollaborativeTripOffer data
     */
    getMyOldCollaborativeTripOffers(): Observable<
        | { data: CollaborativeTripOffer[]; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/my/old',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }

    /**
     * Find matching collaborative trip offers based on passenger requirements
     * @param searchDto Search criteria for finding matching trips
     * @returns Observable with array of matching CollaborativeTripOffer data
     */
    findMatchingCollaborativeTripOffers(
        searchDto: FindMatchingTripsDto,
    ): Observable<
        | { data: CollaborativeTripOffer[]; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/find-matching',
            opj: searchDto,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for search results
        };
        return this.http.post<CollaborativeTripOffer[]>(options);
    }

    /**
     * Book a collaborative trip offer
     * @param offerId ID of the collaborative trip offer to book
     * @param bookDto Booking details
     * @returns Observable with created CollaborativeTripBooking data
     */
    bookOffer(
        offerId: string,
        bookDto: BookOfferDto,
    ): Observable<
        | { data: CollaborativeTripBooking; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/collaborative-trips/book-offer/${offerId}`,
            opj: bookDto,
            des: this.destroyRef,
            successMessage: 'Trip booked successfully',
            failedMessage: 'Failed to book trip',
        };
        return this.http.post<CollaborativeTripBooking>(options);
    }
}
