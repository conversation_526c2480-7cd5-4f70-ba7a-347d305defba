import { CommonModule } from '@angular/common';
import { Component, computed, OnD<PERSON>roy, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { LatLng, latLng } from 'leaflet';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { StepperModule } from 'primeng/stepper';
import { TagModule } from 'primeng/tag';
import { InputNumberModule } from 'primeng/inputnumber';
import { FormsModule } from '@angular/forms';
import {
    MarkedPlace,
    MarkedPlaceService,
} from '../../services/marked-place.service';
import { NominatimService } from '../../services/nominatim.service';
import { StopPoint, StopPointService } from '../../services/stop-point.service';
import {
    CollaborativeTripService,
    CollaborativeTripOffer,
    FindMatchingTripsDto,
    BookOfferDto,
    CollaborativeTripBooking,
} from '../../services/collaborative-trip.service';
import { injectMany } from '../../shared/helpers/injectMany';
import { ReverseGeocodingResult } from '../../shared/types/nominatim.types';
import { LocationPickerComponent } from '../order-flow/location-picker.component';

export enum CollaborativeTripStep {
    SELECT_PICKUP = 0,
    SELECT_DROPOFF = 1,
    SET_PASSENGERS = 2,
    SHOW_RESULTS = 3,
    BOOKING_CONFIRMATION = 4,
}

@Component({
    selector: 'app-scheduled-trip-show',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        StepperModule,
        DialogModule,
        TagModule,
        InputNumberModule,
        FormsModule,
        LocationPickerComponent,
    ],
    templateUrl: './scheduled-trip-show.component.html',
    styles: `
        :host {
            display: block;
            flex-grow: 1;
            max-height: 97dvh;
        }
    `,
    providers: [MessageService],
})
export class ScheduledTripShowComponent implements OnInit, OnDestroy {
    services = injectMany({
        CollaborativeTripService,
        StopPointService,
        MarkedPlaceService,
        MessageService,
        Router,
        NominatimService,
    });

    // Make enum available in template
    CollaborativeTripStep = CollaborativeTripStep;

    // State management
    currentStep = signal<CollaborativeTripStep>(
        CollaborativeTripStep.SELECT_PICKUP,
    );
    pickupLocation = signal<LatLng | null>(null);
    dropoffLocation = signal<LatLng | null>(null);
    pickupAddress = signal<string>('');
    dropoffAddress = signal<string>('');
    pickupAddressDetails = signal<ReverseGeocodingResult | null>(null);
    dropoffAddressDetails = signal<ReverseGeocodingResult | null>(null);
    numberOfPassengers = signal<number>(1);
    passengerNotes = signal<string>('');
    isLoading = signal<boolean>(false);
    markedPlaces = signal<MarkedPlace[]>([]);
    stopPoints = signal<StopPoint[]>([]);
    availableTrips = signal<CollaborativeTripOffer[]>([]);
    selectedTrip = signal<CollaborativeTripOffer | null>(null);
    bookingResult = signal<CollaborativeTripBooking | null>(null);

    // Computed properties
    canProceedToDropoff = computed(() => this.pickupLocation() !== null);
    canProceedToPassengers = computed(
        () => this.pickupLocation() !== null && this.dropoffLocation() !== null,
    );
    canSearchTrips = computed(
        () => this.canProceedToPassengers() && this.numberOfPassengers() > 0,
    );

    ngOnInit(): void {
        this.loadMarkedPlaces();
        this.loadStopPoints();
    }

    ngOnDestroy(): void {
        // Cleanup if needed
    }

    /**
     * Load user's marked places
     */
    loadMarkedPlaces(): void {
        this.services.MarkedPlaceService.getAllMarkedPlaces().subscribe({
            next: (response) => {
                if (response.data) {
                    this.markedPlaces.set(response.data);
                }
            },
            error: (error) => {
                console.error('Failed to load marked places:', error);
                // Don't show error toast as this is not critical
            },
        });
    }

    /**
     * Load all stop points
     */
    loadStopPoints(): void {
        this.services.StopPointService.getAllStopPoints().subscribe({
            next: (response) => {
                if (response.data) {
                    this.stopPoints.set(response.data);
                }
            },
            error: (error) => {
                console.error('Failed to load stop points:', error);
                // Don't show error toast as this is not critical
            },
        });
    }

    /**
     * Select a marked place as pickup location
     */
    selectPickupMarkedPlace(markedPlace: MarkedPlace): void {
        const location = latLng(markedPlace.latitude, markedPlace.longitude);
        this.pickupLocation.set(location);
        this.pickupAddress.set(markedPlace.name);
        this.currentStep.set(CollaborativeTripStep.SELECT_DROPOFF);
    }

    /**
     * Select a stop point as pickup location
     */
    selectPickupStopPoint(stopPoint: StopPoint): void {
        const location = latLng(stopPoint.latitude, stopPoint.longitude);
        this.pickupLocation.set(location);
        this.pickupAddress.set(stopPoint.name);
        this.currentStep.set(CollaborativeTripStep.SELECT_DROPOFF);
    }

    /**
     * Select a marked place as dropoff location
     */
    selectDropoffMarkedPlace(markedPlace: MarkedPlace): void {
        const location = latLng(markedPlace.latitude, markedPlace.longitude);
        this.dropoffLocation.set(location);
        this.dropoffAddress.set(markedPlace.name);
        this.currentStep.set(CollaborativeTripStep.SET_PASSENGERS);
    }

    /**
     * Select a stop point as dropoff location
     */
    selectDropoffStopPoint(stopPoint: StopPoint): void {
        const location = latLng(stopPoint.latitude, stopPoint.longitude);
        this.dropoffLocation.set(location);
        this.dropoffAddress.set(stopPoint.name);
        this.currentStep.set(CollaborativeTripStep.SET_PASSENGERS);
    }

    /**
     * Handle location selection from map
     */
    onLocationSelected(location: LatLng): void {
        if (this.currentStep() === CollaborativeTripStep.SELECT_PICKUP) {
            this.pickupLocation.set(location);

            // Get address for the location using reverse geocoding
            this.services.NominatimService.reverseGeocode(
                location.lng,
                location.lat,
            ).subscribe({
                next: (result) => {
                    if (result && result.display_name) {
                        this.pickupAddress.set(result.display_name);
                        this.pickupAddressDetails.set(result);
                    } else {
                        this.pickupAddress.set('Selected location');
                        this.pickupAddressDetails.set(null);
                    }
                    this.currentStep.set(CollaborativeTripStep.SELECT_DROPOFF);
                },
                error: () => {
                    this.pickupAddress.set('Selected location');
                    this.pickupAddressDetails.set(null);
                    this.currentStep.set(CollaborativeTripStep.SELECT_DROPOFF);
                },
            });
        } else if (
            this.currentStep() === CollaborativeTripStep.SELECT_DROPOFF
        ) {
            this.dropoffLocation.set(location);

            // Get address for the location using reverse geocoding
            this.services.NominatimService.reverseGeocode(
                location.lng,
                location.lat,
            ).subscribe({
                next: (result) => {
                    if (result && result.display_name) {
                        this.dropoffAddress.set(result.display_name);
                        this.dropoffAddressDetails.set(result);
                    } else {
                        this.dropoffAddress.set('Selected location');
                        this.dropoffAddressDetails.set(null);
                    }
                    this.currentStep.set(CollaborativeTripStep.SET_PASSENGERS);
                },
                error: () => {
                    this.dropoffAddress.set('Selected location');
                    this.dropoffAddressDetails.set(null);
                    this.currentStep.set(CollaborativeTripStep.SET_PASSENGERS);
                },
            });
        }
    }

    /**
     * Search for matching collaborative trips
     */
    searchMatchingTrips(): void {
        const pickup = this.pickupLocation();
        const dropoff = this.dropoffLocation();
        const passengers = this.numberOfPassengers();

        if (!pickup || !dropoff || passengers <= 0) {
            this.services.MessageService.add({
                severity: 'error',
                summary: 'Missing Information',
                detail: 'Please select pickup and dropoff locations and set number of passengers.',
            });
            return;
        }

        this.isLoading.set(true);

        const searchDto: FindMatchingTripsDto = {
            pickupLatitude: pickup.lat,
            pickupLongitude: pickup.lng,
            dropoffLatitude: dropoff.lat,
            dropoffLongitude: dropoff.lng,
            passengers: passengers,
        };

        this.services.CollaborativeTripService.findMatchingCollaborativeTripOffers(
            searchDto,
        ).subscribe({
            next: (response) => {
                this.isLoading.set(false);
                if (response.data) {
                    this.availableTrips.set(response.data);
                    this.currentStep.set(CollaborativeTripStep.SHOW_RESULTS);
                } else {
                    this.services.MessageService.add({
                        severity: 'info',
                        summary: 'No Trips Found',
                        detail: 'No matching collaborative trips found for your criteria.',
                    });
                }
            },
            error: (error) => {
                this.isLoading.set(false);
                console.error('Error searching for trips:', error);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Search Failed',
                    detail: 'Failed to search for collaborative trips. Please try again.',
                });
            },
        });
    }

    /**
     * Book a collaborative trip
     */
    bookTrip(trip: CollaborativeTripOffer): void {
        const pickup = this.pickupLocation();
        const dropoff = this.dropoffLocation();
        const passengers = this.numberOfPassengers();

        if (!pickup || !dropoff || passengers <= 0) {
            this.services.MessageService.add({
                severity: 'error',
                summary: 'Missing Information',
                detail: 'Invalid booking information.',
            });
            return;
        }

        this.isLoading.set(true);
        this.selectedTrip.set(trip);

        const bookDto: BookOfferDto = {
            pickupLatitude: pickup.lat,
            pickupLongitude: pickup.lng,
            dropoffLatitude: dropoff.lat,
            dropoffLongitude: dropoff.lng,
            passengers: passengers,
            passengerNotes: this.passengerNotes(),
        };

        this.services.CollaborativeTripService.bookOffer(
            trip.id,
            bookDto,
        ).subscribe({
            next: (response) => {
                this.isLoading.set(false);
                if (response.data) {
                    this.bookingResult.set(response.data);
                    this.currentStep.set(
                        CollaborativeTripStep.BOOKING_CONFIRMATION,
                    );
                } else {
                    this.services.MessageService.add({
                        severity: 'error',
                        summary: 'Booking Failed',
                        detail: 'Failed to book the trip. Please try again.',
                    });
                }
            },
            error: (error) => {
                this.isLoading.set(false);
                console.error('Error booking trip:', error);
                let errorMessage = 'Failed to book the trip. Please try again.';
                if (error?.error?.message) {
                    errorMessage = error.error.message;
                }
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Booking Failed',
                    detail: errorMessage,
                });
            },
        });
    }

    /**
     * Go back to previous step
     */
    goBack(): void {
        const current = this.currentStep();
        switch (current) {
            case CollaborativeTripStep.SELECT_DROPOFF:
                this.currentStep.set(CollaborativeTripStep.SELECT_PICKUP);
                break;
            case CollaborativeTripStep.SET_PASSENGERS:
                this.currentStep.set(CollaborativeTripStep.SELECT_DROPOFF);
                break;
            case CollaborativeTripStep.SHOW_RESULTS:
                this.currentStep.set(CollaborativeTripStep.SET_PASSENGERS);
                break;
            case CollaborativeTripStep.BOOKING_CONFIRMATION:
                this.currentStep.set(CollaborativeTripStep.SHOW_RESULTS);
                break;
        }
    }

    /**
     * Start over - reset everything
     */
    startOver(): void {
        this.pickupLocation.set(null);
        this.dropoffLocation.set(null);
        this.pickupAddress.set('');
        this.dropoffAddress.set('');
        this.pickupAddressDetails.set(null);
        this.dropoffAddressDetails.set(null);
        this.numberOfPassengers.set(1);
        this.passengerNotes.set('');
        this.availableTrips.set([]);
        this.selectedTrip.set(null);
        this.bookingResult.set(null);
        this.currentStep.set(CollaborativeTripStep.SELECT_PICKUP);
    }

    /**
     * Navigate to main page
     */
    goToMain(): void {
        this.services.Router.navigate(['/main']);
    }

    /**
     * Format date for display
     */
    formatDate(dateString: string): string {
        return new Date(dateString).toLocaleString();
    }

    /**
     * Calculate available seats for a trip
     */
    getAvailableSeats(trip: CollaborativeTripOffer): number {
        const totalBooked = trip.totalBookedSeats || 0;
        return Math.max(0, trip.availableSeats - totalBooked);
    }

    /**
     * Check if trip has enough seats for current passenger count
     */
    hasEnoughSeats(trip: CollaborativeTripOffer): boolean {
        return this.getAvailableSeats(trip) >= this.numberOfPassengers();
    }
}
