import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { ValhallaService } from '../pricing/valhalla.service';
import { CreateCollaborativeTripOfferDto } from './dto/create-collaborative-trip-offer.dto';
import { FindMatchingTripsDto } from './dto/find-matching-trips.dto';
import { BookOfferDto } from './dto/book-offer.dto';

@Injectable()
export class CollaborativeTripService {
  private readonly logger = new Logger(CollaborativeTripService.name);
  private readonly NEARBY_STOP_POINTS_RADIUS_KMS = 0.3;
  private readonly PICKUP_DROPOFF_RADIUS_KMS = 0.2;

  constructor(
    private prisma: PrismaService,
    private valhallaService: ValhallaService,
  ) { }

  async createCollaborativeTripOffer(
    driverId: string,
    createDto: CreateCollaborativeTripOfferDto,
  ): Promise<any> {
    try {
      // 1. Create start point
      const startPoint = await this.prisma.point.create({
        data: {
          latitude: createDto.startLatitude,
          longitude: createDto.startLongitude,
        },
      });

      // 2. Create end point
      const endPoint = await this.prisma.point.create({
        data: {
          latitude: createDto.endLatitude,
          longitude: createDto.endLongitude,
        },
      });

      // 3. Create waypoints if provided
      const waypoints: any[] = [];
      if (createDto.requiredWaypoints && createDto.requiredWaypoints.length > 0) {
        for (const waypoint of createDto.requiredWaypoints) {
          const point = await this.prisma.point.create({
            data: {
              latitude: waypoint.latitude,
              longitude: waypoint.longitude,
            },
          });
          waypoints.push(point);
        }
      }

      // 4. Find nearby stop points along the route
      const nearbyStopPoints = await this.findNearbyStopPointsAlongRoute(
        createDto.routePoints || []
      );

      // 5. Create the collaborative trip offer
      const collaborativeTripOffer = await this.prisma.collaborativeTripOffer.create({
        data: {
          driverId,
          scheduledTime: new Date(createDto.scheduledTime),
          startpoint: startPoint.id,
          endpoint: endPoint.id,
          routePoints: createDto.routePoints || [],
          totalTime: 0, // This should be calculated from Valhalla if needed
          availableSeats: createDto.availableSeats,
          pricePerSeat: createDto.pricePerSeat,
          clientBookings: [], // Initialize with empty array
          possiblePickupPoints: createDto.possiblePickupPoints || [],
          possibleDropoffPoints: createDto.possibleDropoffPoints || [],
          driverNotes: createDto.driverNotes,
          waypoints: {
            connect: waypoints.map(wp => ({ id: wp.id }))
          }
        },
        include: {
          startPoint: true,
          endPoint: true,
          waypoints: true,
          driver: true,
        }
      });

      this.logger.log(`Created collaborative trip offer: ${collaborativeTripOffer.id}`);
      return collaborativeTripOffer;

    } catch (error) {
      this.logger.error(`Failed to create collaborative trip offer: ${error.message}`);
      throw new BadRequestException('Failed to create collaborative trip offer');
    }
  }

  /**
   * Find nearby stop points along a route
   */
  private async findNearbyStopPointsAlongRoute(
    routePoints: Array<{ lat: number; lng: number }>,
  ): Promise<any[]> {
    if (!routePoints || routePoints.length === 0) {
      return [];
    }

    const allStopPoints = await this.prisma.stopPoint.findMany();
    const nearbyStops: any[] = [];

    for (const stopPoint of allStopPoints) {
      let minDistance = Infinity;

      // Find the closest route point to this stop point
      for (const routePoint of routePoints) {
        const distance = this.calculateDistance(
          stopPoint.latitude,
          stopPoint.longitude,
          routePoint.lat,
          routePoint.lng,
        );

        if (distance < minDistance) {
          minDistance = distance;
        }
      }

      // If stop point is within threshold, add it
      if (minDistance <= this.NEARBY_STOP_POINTS_RADIUS_KMS) {
        nearbyStops.push(stopPoint);
      }
    }

    return nearbyStops;
  }

  /**
   * Get recent collaborative trip offers for a driver
   * Recent offers are those scheduled within the next 24 hours
   */
  async getMyRecentCollaborativeTripOffers(driverId: string): Promise<any[]> {
    try {
      const now = new Date();
      const twentyFourHoursFromNow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      const recentOffers = await this.prisma.collaborativeTripOffer.findMany({
        where: {
          driverId,
          scheduledTime: {
            gte: now,
            lte: twentyFourHoursFromNow,
          },
        },
        include: {
          startPoint: true,
          endPoint: true,
          waypoints: true,
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phoneNumber: true,
            },
          },
        },
        orderBy: {
          scheduledTime: 'asc',
        },
      });

      return recentOffers;
    } catch (error) {
      this.logger.error(`Failed to get recent collaborative trip offers: ${error.message}`);
      throw new BadRequestException('Failed to get recent collaborative trip offers');
    }
  }

  /**
   * Get old collaborative trip offers for a driver
   * Old offers are those scheduled more than 24 hours ago
   */
  async getMyOldCollaborativeTripOffers(driverId: string): Promise<any[]> {
    try {
      const now = new Date();
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const oldOffers = await this.prisma.collaborativeTripOffer.findMany({
        where: {
          driverId,
          scheduledTime: {
            lt: twentyFourHoursAgo,
          },
        },
        include: {
          startPoint: true,
          endPoint: true,
          waypoints: true,
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phoneNumber: true,
            },
          },
        },
        orderBy: {
          scheduledTime: 'desc',
        },
      });

      return oldOffers;
    } catch (error) {
      this.logger.error(`Failed to get old collaborative trip offers: ${error.message}`);
      throw new BadRequestException('Failed to get old collaborative trip offers');
    }
  }

  /**
   * Find matching collaborative trip offers based on client requirements
   */
  async findMatchingCollaborativeTripOffers(searchDto: FindMatchingTripsDto): Promise<any[]> {
    try {
      // 1. Get current time in Syrian timezone (UTC+3)
      const now = new Date();
      const syrianTime = new Date(now.getTime() + 3 * 60 * 60 * 1000); // UTC+3

      // 2. Get all future collaborative trip offers
      const allOffers = await this.prisma.collaborativeTripOffer.findMany({
        where: {
          scheduledTime: {
            gt: syrianTime,
          },
          availableSeats: {
            gte: searchDto.passengers,
          },
        },
        include: {
          startPoint: true,
          endPoint: true,
          waypoints: true,
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phoneNumber: true,
            },
          },
        },
      });

      const matchingOffers: any[] = [];

      for (const offer of allOffers) {
        let isMatch = true;

        // 3. Check if pickup point is within 0.2km of any possiblePickupPoints
        if (offer.possiblePickupPoints && offer.possiblePickupPoints.length > 0) {
          const pickupPoints = await this.prisma.stopPoint.findMany({
            where: {
              id: { in: offer.possiblePickupPoints },
            },
          });

          let pickupMatch = false;
          for (const pickupPoint of pickupPoints) {
            const distance = this.calculateDistance(
              searchDto.pickupLatitude,
              searchDto.pickupLongitude,
              pickupPoint.latitude,
              pickupPoint.longitude,
            );
            if (distance <= this.PICKUP_DROPOFF_RADIUS_KMS) {
              pickupMatch = true;
              break;
            }
          }
          if (!pickupMatch) {
            isMatch = false;
          }
        } else {
          isMatch = false;
        }

        // 4. Check if dropoff point is within 0.2km of any possibleDropoffPoints
        if (isMatch && offer.possibleDropoffPoints && offer.possibleDropoffPoints.length > 0) {
          const dropoffPoints = await this.prisma.stopPoint.findMany({
            where: {
              id: { in: offer.possibleDropoffPoints },
            },
          });

          let dropoffMatch = false;
          for (const dropoffPoint of dropoffPoints) {
            const distance = this.calculateDistance(
              searchDto.dropoffLatitude,
              searchDto.dropoffLongitude,
              dropoffPoint.latitude,
              dropoffPoint.longitude,
            );
            if (distance <= this.PICKUP_DROPOFF_RADIUS_KMS) {
              dropoffMatch = true;
              break;
            }
          }
          if (!dropoffMatch) {
            isMatch = false;
          }
        } else {
          isMatch = false;
        }

        if (isMatch) {
          // Add clientBookings info to the response
          const clientBookings = offer.clientBookings as any[] || [];
          const totalBookedSeats = clientBookings.reduce((sum, booking) => sum + booking.seatsBooked, 0);
          
          matchingOffers.push({
            ...offer,
            totalBookedSeats,
            clientBookings,
          });
        }
      }

      // Sort by scheduled time (earliest first)
      matchingOffers.sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime());

      return matchingOffers;
    } catch (error) {
      this.logger.error(`Failed to find matching collaborative trip offers: ${error.message}`);
      throw new BadRequestException('Failed to find matching collaborative trip offers');
    }
  }

  /**
   * Book a collaborative trip offer
   */
  async bookOffer(offerId: string, bookDto: BookOfferDto, clientId: string): Promise<any> {
    try {
      // 1. Get the offer and verify it exists
      const offer = await this.prisma.collaborativeTripOffer.findUnique({
        where: { id: offerId },
        include: {
          startPoint: true,
          endPoint: true,
          possiblePickupPoints: true,
          possibleDropoffPoints: true,
        },
      });

      if (!offer) {
        throw new BadRequestException('Collaborative trip offer not found');
      }

      // 2. Check if the offer is still available (scheduled in future)
      const now = new Date();
      const syrianTime = new Date(now.getTime() + 3 * 60 * 60 * 1000); // UTC+3
      if (new Date(offer.scheduledTime) <= syrianTime) {
        throw new BadRequestException('This offer is no longer available');
      }

      // 3. Check if there are enough available seats
      if (offer.availableSeats < bookDto.passengers) {
        throw new BadRequestException('Not enough available seats');
      }

      // 4. Verify pickup point is within range of possiblePickupPoints
      let pickupMatch = false;
      let pickupPointId = '';
      
      if (offer.possiblePickupPoints && offer.possiblePickupPoints.length > 0) {
        const pickupPoints = await this.prisma.stopPoint.findMany({
          where: {
            id: { in: offer.possiblePickupPoints },
          },
        });

        for (const pickupPoint of pickupPoints) {
          const distance = this.calculateDistance(
            bookDto.pickupLatitude,
            bookDto.pickupLongitude,
            pickupPoint.latitude,
            pickupPoint.longitude,
          );
          if (distance <= this.PICKUP_DROPOFF_RADIUS_KMS) {
            pickupMatch = true;
            pickupPointId = pickupPoint.id;
            break;
          }
        }
      }

      if (!pickupMatch) {
        throw new BadRequestException('Pickup point is too far from available pickup locations');
      }

      // 5. Verify dropoff point is within range of possibleDropoffPoints
      let dropoffMatch = false;
      let dropoffPointId = '';
      
      if (offer.possibleDropoffPoints && offer.possibleDropoffPoints.length > 0) {
        const dropoffPoints = await this.prisma.stopPoint.findMany({
          where: {
            id: { in: offer.possibleDropoffPoints },
          },
        });

        for (const dropoffPoint of dropoffPoints) {
          const distance = this.calculateDistance(
            bookDto.dropoffLatitude,
            bookDto.dropoffLongitude,
            dropoffPoint.latitude,
            dropoffPoint.longitude,
          );
          if (distance <= this.PICKUP_DROPOFF_RADIUS_KMS) {
            dropoffMatch = true;
            dropoffPointId = dropoffPoint.id;
            break;
          }
        }
      }

      if (!dropoffMatch) {
        throw new BadRequestException('Dropoff point is too far from available dropoff locations');
      }

      // 6. Create the booking
      const booking = await this.prisma.collaborativeTripBooking.create({
        data: {
          collaborativeTripOfferId: offerId,
          passengerId: clientId,
          numberOfPassengers: bookDto.passengers,
          pickupPointId: pickupPointId,
          dropoffPointId: dropoffPointId,
          totalCost: offer.pricePerSeat * bookDto.passengers,
          passengerNotes: bookDto.passengerNotes,
        },
        include: {
          collaborativeTripOffer: true,
          passenger: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phoneNumber: true,
            },
          },
          pickupPoint: true,
          dropoffPoint: true,
        },
      });

      // 7. Update the offer's available seats and clientBookings
      const currentClientBookings = offer.clientBookings as any[] || [];
      const newClientBooking = {
        clientId,
        seatsBooked: bookDto.passengers,
        pickupPointId,
        dropoffPointId,
        status: 'BOOKED',
        bookingId: booking.id,
        createdAt: new Date(),
      };
      
      currentClientBookings.push(newClientBooking);

      await this.prisma.collaborativeTripOffer.update({
        where: { id: offerId },
        data: {
          availableSeats: offer.availableSeats - bookDto.passengers,
          clientBookings: currentClientBookings,
        },
      });

      this.logger.log(`Client ${clientId} booked offer ${offerId} for ${bookDto.passengers} passengers`);
      return booking;

    } catch (error) {
      this.logger.error(`Failed to book collaborative trip offer: ${error.message}`);
      throw new BadRequestException(error.message || 'Failed to book collaborative trip offer');
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
      Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
} 