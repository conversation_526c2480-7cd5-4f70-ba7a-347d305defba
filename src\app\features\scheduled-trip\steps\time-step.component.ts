import { CommonModule } from '@angular/common';
import {
    Component,
    input,
    output,
    signal,
    computed,
    effect,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { InputNumber } from 'primeng/inputnumber';
import { ScheduledTripData } from '../scheduled-trip-steps.component';
import { DatePicker } from 'primeng/datepicker';

interface DayOfWeek {
    label: string;
    value: number;
    shortLabel: string;
}

@Component({
    selector: 'app-time-step',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ButtonModule,
        CardModule,
        DatePicker,
        CheckboxModule,
        InputNumber,
    ],
    template: `
        <div class="flex flex-1 flex-col bg-background-color-100">
            <!-- Step Header -->
            <div
                class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
            >
                <div
                    class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-white"
                >
                    <i class="pi pi-calendar text-lg"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-text-color-100">
                        When do you want to travel?
                    </h3>
                    <p class="text-sm text-text-color-300">
                        Select your preferred date and time
                    </p>
                </div>
            </div>

            <div class="flex flex-1 flex-col justify-center p-6">
                <div class="mx-auto w-full max-w-md space-y-6">
                    <!-- Date Selection -->
                    <div class="space-y-3">
                        <label
                            class="block text-sm font-semibold text-text-color-100"
                        >
                            Select Date & Time
                        </label>
                        <p-datepicker
                            [(ngModel)]="selectedDate"
                            [minDate]="minDate"
                            [maxDate]="maxDate"
                            [showTime]="true"
                            [hourFormat]="'24'"
                            [showIcon]="true"
                            iconDisplay="input"
                            placeholder="Choose a date and time"
                            (onSelect)="onDateChange()"
                            [appendTo]="'body'"
                        ></p-datepicker>
                    </div>

                    <!-- Trip Details -->
                    <div class="space-y-4">
                        <h4 class="text-sm font-semibold text-text-color-100">
                            Trip Details
                        </h4>

                        <!-- Available Seats -->
                        <div class="space-y-2">
                            <label
                                class="block text-sm font-medium text-text-color-100"
                            >
                                Available Seats
                            </label>
                            <p-inputnumber
                                [(ngModel)]="availableSeats"
                                [min]="1"
                                [max]="8"
                                [showButtons]="true"
                                buttonLayout="horizontal"
                                spinnerMode="horizontal"
                                placeholder="Number of available seats"
                                styleClass="w-full"
                                inputStyleClass="w-full"
                            ></p-inputnumber>
                            <p class="text-xs text-text-color-300">
                                How many seats are available for passengers?
                            </p>
                        </div>

                        <!-- Price Per Seat -->
                        <div class="space-y-2">
                            <label
                                class="block text-sm font-medium text-text-color-100"
                            >
                                Price Per Seat ($)
                            </label>
                            <p-inputnumber
                                [(ngModel)]="pricePerSeat"
                                [min]="1"
                                [max]="100"
                                [showButtons]="true"
                                buttonLayout="horizontal"
                                spinnerMode="horizontal"
                                placeholder="Price per seat"
                                styleClass="w-full"
                                inputStyleClass="w-full"
                                [step]="0.5"
                                [minFractionDigits]="2"
                                [maxFractionDigits]="2"
                            ></p-inputnumber>
                            <p class="text-xs text-text-color-300">
                                How much should each passenger pay?
                            </p>
                        </div>
                    </div>

                    <!--
                    <div class="space-y-3">
                        <label
                            class="block text-sm font-semibold text-text-color-100"
                        >
                            Select Days of the Week
                        </label>
                        <p class="text-xs text-text-color-300">
                            Choose which days of the week you want to schedule
                            this trip
                        </p>
                        <div class="grid grid-cols-7 gap-2">
                            @for (day of daysOfWeek; track day.value) {
                                <div class="flex flex-col items-center">
                                    <label
                                        class="text-xs text-text-color-300 mb-1"
                                    >
                                        {{ day.shortLabel }}
                                    </label>
                                    <p-checkbox
                                        [binary]="true"
                                        [ngModel]="isDaySelected(day.value)"
                                        (ngModelChange)="
                                            toggleDayOfWeek(day.value)
                                        "
                                        [inputId]="'day-' + day.value"
                                        styleClass="day-checkbox"
                                    ></p-checkbox>
                                </div>
                            }
                        </div>
                        @if (selectedDaysOfWeek().length > 0) {
                            <div class="mt-2 text-xs text-text-color-300">
                                Selected:
                                @for (
                                    dayValue of selectedDaysOfWeek();
                                    track dayValue;
                                    let isLast = $last
                                ) {
                                    <span
                                        >{{ daysOfWeek[dayValue].label }}
                                        @if (!isLast) {
                                            <span>, </span>
                                        }
                                    </span>
                                }
                            </div>
                        }
                    </div> -->

                    <!-- Selected DateTime Display -->
                    @if (selectedDateTime()) {
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <h4
                                class="mb-2 text-sm font-semibold text-text-color-100"
                            >
                                Selected Schedule
                            </h4>
                            <div
                                class="flex items-center gap-2 text-sm text-text-color-300"
                            >
                                <i
                                    class="pi pi-calendar text-main-color-600"
                                ></i>
                                <span>{{
                                    selectedDateTime() | date: 'fullDate'
                                }}</span>
                            </div>
                            <div
                                class="mt-1 flex items-center gap-2 text-sm text-text-color-300"
                            >
                                <i class="pi pi-clock text-main-color-600"></i>
                                <span>{{
                                    selectedDateTime() | date: 'shortTime'
                                }}</span>
                            </div>
                        </div>
                    }

                    <!-- Quick Time Options -->
                    <div class="space-y-3">
                        <label
                            class="block text-sm font-semibold text-text-color-100"
                        >
                            Quick Options
                        </label>
                        <div class="grid grid-cols-2 gap-3">
                            <button
                                class="rounded-lg border border-background-color-300 bg-background-color-100 p-3 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-black"
                                (click)="setQuickTime('tomorrow', '08:00')"
                            >
                                <i class="pi pi-sun mr-2"></i>
                                Tomorrow Morning
                            </button>
                            <button
                                class="rounded-lg border border-background-color-300 bg-background-color-100 p-3 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-black"
                                (click)="setQuickTime('tomorrow', '18:00')"
                            >
                                <i class="pi pi-moon mr-2"></i>
                                Tomorrow Evening
                            </button>
                            <button
                                class="rounded-lg border border-background-color-300 bg-background-color-100 p-3 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-black"
                                (click)="setQuickTime('nextWeek', '09:00')"
                            >
                                <i class="pi pi-calendar-plus mr-2"></i>
                                Next Week
                            </button>
                            <button
                                class="rounded-lg border border-background-color-300 bg-background-color-100 p-3 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-black"
                                (click)="setQuickTime('weekend', '10:00')"
                            >
                                <i class="pi pi-heart mr-2"></i>
                                This Weekend
                            </button>
                        </div>
                    </div>

                    <!-- Continue Button -->
                    @if (canProceed()) {
                        <button
                            class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg border-none bg-background-color-200 text-base font-semibold text-white shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400"
                            (click)="confirmTime()"
                        >
                            <i class="pi pi-arrow-right mr-2 text-sm"></i>
                            Continue to Near Pickup Location
                        </button>
                    }
                </div>
            </div>
        </div>
    `,
})
export class TimeStepComponent {
    // Inputs
    tripData = input<ScheduledTripData>({});

    // Outputs
    completed = output<{
        dateTime: Date;
        daysOfWeek: number[];
        availableSeats: number;
        pricePerSeat: number;
    }>();

    // State
    selectedDate = signal<Date | null>(null);
    selectedTime = signal<string | null>(null);
    selectedDateTime = signal<Date | null>(null);
    selectedDaysOfWeek = signal<number[]>([]);
    availableSeats = signal<number>(3); // Default to 3 seats
    pricePerSeat = signal<number>(10); // Default to $10 per seat

    // Days of the week data
    daysOfWeek: DayOfWeek[] = [
        { label: 'Sunday', shortLabel: 'Sun', value: 0 },
        { label: 'Monday', shortLabel: 'Mon', value: 1 },
        { label: 'Tuesday', shortLabel: 'Tue', value: 2 },
        { label: 'Wednesday', shortLabel: 'Wed', value: 3 },
        { label: 'Thursday', shortLabel: 'Thu', value: 4 },
        { label: 'Friday', shortLabel: 'Fri', value: 5 },
        { label: 'Saturday', shortLabel: 'Sat', value: 6 },
    ];

    // Date constraints
    minDate = new Date(); // Today
    maxDate = (() => {
        const date = new Date();
        date.setMonth(date.getMonth() + 3); // 3 months from now
        return date;
    })();

    // Computed
    canProceed = computed(
        () => this.selectedDateTime() !== null,
        // &&     this.selectedDaysOfWeek().length > 0,
    );

    constructor() {
        // Initialize from tripData when it changes
        effect(() => {
            const data = this.tripData();
            if (data.scheduledDateTime) {
                this.selectedDate.set(data.scheduledDateTime);
                this.selectedDateTime.set(data.scheduledDateTime);
            }
            if (data.selectedDaysOfWeek) {
                this.selectedDaysOfWeek.set([...data.selectedDaysOfWeek]);
            }
            if (data.availableSeats !== undefined) {
                this.availableSeats.set(data.availableSeats);
            }
            if (data.pricePerSeat !== undefined) {
                this.pricePerSeat.set(data.pricePerSeat);
            }
        });
    }

    onDateChange(): void {
        const date = this.selectedDate();
        if (date) {
            // The datepicker with showTime=true returns a complete Date object
            this.selectedDateTime.set(date);
        } else {
            this.selectedDateTime.set(null);
        }
    }

    onTimeChange(): void {
        this.updateDateTime();
    }

    private updateDateTime(): void {
        const date = this.selectedDate();
        const time = this.selectedTime();

        if (date && time) {
            const [hours, minutes] = time.split(':').map(Number);
            const dateTime = new Date(date);
            dateTime.setHours(hours, minutes, 0, 0);
            this.selectedDateTime.set(dateTime);
        } else if (date) {
            // If we have a date but no separate time, use the date as-is
            this.selectedDateTime.set(date);
        } else {
            this.selectedDateTime.set(null);
        }
    }

    setQuickTime(
        type: 'tomorrow' | 'nextWeek' | 'weekend',
        time: string,
    ): void {
        const now = new Date();
        let targetDate: Date;

        switch (type) {
            case 'tomorrow':
                targetDate = new Date(now);
                targetDate.setDate(now.getDate() + 1);
                break;
            case 'nextWeek':
                targetDate = new Date(now);
                targetDate.setDate(now.getDate() + 7);
                break;
            case 'weekend':
                targetDate = new Date(now);
                const daysUntilSaturday = (6 - now.getDay()) % 7;
                targetDate.setDate(now.getDate() + (daysUntilSaturday || 7));
                break;
            default:
                return;
        }

        // Set the time on the target date
        const [hours, minutes] = time.split(':').map(Number);
        targetDate.setHours(hours, minutes, 0, 0);

        this.selectedDate.set(targetDate);
        this.selectedDateTime.set(targetDate);
    }

    toggleDayOfWeek(dayValue: number): void {
        const currentDays = this.selectedDaysOfWeek();
        const index = currentDays.indexOf(dayValue);

        if (index > -1) {
            // Remove the day
            const newDays = currentDays.filter((day) => day !== dayValue);
            this.selectedDaysOfWeek.set(newDays);
        } else {
            // Add the day
            const newDays = [...currentDays, dayValue].sort();
            this.selectedDaysOfWeek.set(newDays);
        }
    }

    isDaySelected(dayValue: number): boolean {
        return this.selectedDaysOfWeek().includes(dayValue);
    }

    confirmTime(): void {
        const dateTime = this.selectedDateTime();
        if (dateTime) {
            this.completed.emit({
                dateTime,
                daysOfWeek: [],
                availableSeats: this.availableSeats(),
                pricePerSeat: this.pricePerSeat(),
            });
        }
        // const dateTime = this.selectedDateTime();
        // const daysOfWeek = this.selectedDaysOfWeek();
        // if (dateTime && daysOfWeek.length > 0) {
        //     this.completed.emit({ dateTime, daysOfWeek });
        // }
    }
}
